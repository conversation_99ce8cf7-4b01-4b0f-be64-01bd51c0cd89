{% extends "base.html" %} {% block title %}FAQs - Talaria Dashboard{% endblock
%} {% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
  <!-- Header -->
  <div class="mb-8">
    <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
      Frequently Asked Questions
    </h1>
    <p class="mt-2 text-lg text-gray-600 dark:text-gray-300">
      Find answers to common questions about the Talaria Dashboard system and
      its comprehensive features
    </p>
  </div>

  <!-- Search Box -->
  <div class="mb-8">
    <div class="relative">
      <input
        type="text"
        id="faqSearch"
        class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        placeholder="Search FAQs..."
      />
      <span class="absolute right-3 top-2.5 text-gray-400">
        <i class="fas fa-search"></i>
      </span>
    </div>
  </div>

  <!-- FAQ Categories -->
  <div class="space-y-8">
    {% for category, content in faqs.items() %}
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm overflow-hidden">
      <div class="p-6">
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
          {{ content.title }}
        </h2>
        <div class="space-y-4">
          {% for item in content.questions %}
          <!-- Changed from content.items to content.questions -->
          <div class="faq-item" data-category="{{ category }}">
            <button class="faq-question w-full text-left focus:outline-none">
              <div class="flex items-center justify-between">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                  {{ item.question }}
                </h3>
                <span class="ml-6 h-7 flex items-center">
                  <i
                    class="fas fa-chevron-down transform transition-transform"
                  ></i>
                </span>
              </div>
            </button>
            <div class="faq-answer mt-2 hidden">
              <p class="text-gray-600 dark:text-gray-400">{{ item.answer }}</p>
            </div>
          </div>
          {% endfor %}
        </div>
      </div>
    </div>
    {% endfor %}
  </div>

  <!-- Contact Support Section -->
  <div class="mt-12 bg-blue-50 dark:bg-blue-900 rounded-lg p-6">
    <div class="flex items-center justify-between">
      <div>
        <h3 class="text-lg font-medium text-blue-900 dark:text-blue-100">
          Still have questions?
        </h3>
        <p class="mt-1 text-blue-700 dark:text-blue-200">
          Contact our support team for additional help with Talaria Dashboard
        </p>
      </div>
      <a
        href="mailto:<EMAIL>"
        class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
      >
        Contact Support
      </a>
    </div>
  </div>
</div>

<script>
  document.addEventListener("DOMContentLoaded", function () {
    // FAQ Accordion functionality
    document.querySelectorAll(".faq-question").forEach((button) => {
      button.addEventListener("click", () => {
        const answer = button.nextElementSibling;
        const icon = button.querySelector(".fas");

        // Toggle answer visibility
        answer.classList.toggle("hidden");

        // Rotate icon
        icon.classList.toggle("rotate-180");
      });
    });

    // Search functionality
    const searchInput = document.getElementById("faqSearch");
    const faqItems = document.querySelectorAll(".faq-item");

    searchInput.addEventListener("input", function (e) {
      const searchTerm = e.target.value.toLowerCase();

      faqItems.forEach((item) => {
        const question = item
          .querySelector(".faq-question h3")
          .textContent.toLowerCase();
        const answer = item
          .querySelector(".faq-answer p")
          .textContent.toLowerCase();

        if (question.includes(searchTerm) || answer.includes(searchTerm)) {
          item.style.display = "block";
        } else {
          item.style.display = "none";
        }
      });
    });
  });
</script>
{% endblock %}
