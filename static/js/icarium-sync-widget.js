/**
 * Icarium Sync Widget
 * Dashboard widget for monitoring new wafers and sync status
 */

class IcariumSyncWidget {
  constructor(containerId) {
    this.containerId = containerId;
    this.container = document.getElementById(containerId);
    this.refreshInterval = 5 * 60 * 1000; // 5 minutes
    this.intervalId = null;
    this.init();
  }

  init() {
    if (!this.container) {
      console.error(`❌ Container ${this.containerId} not found`);
      return;
    }

    this.createWidget();
    this.loadData();
    this.startAutoRefresh();
    console.log('🔗 Icarium Sync Widget initialized');
  }

  createWidget() {
    this.container.innerHTML = `
      <div class="icarium-sync-widget">
        <div class="widget-header">
          <h3 class="widget-title">
            <i class="fas fa-sync-alt"></i>
            Icarium Sync Status
          </h3>
          <div class="widget-actions">
            <button class="btn-refresh" onclick="icariumSyncWidget.loadData()">
              <i class="fas fa-refresh"></i>
            </button>
            <button class="btn-settings" onclick="icariumSyncWidget.showSettings()">
              <i class="fas fa-cog"></i>
            </button>
          </div>
        </div>
        
        <div class="widget-content">
          <div class="loading-state">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Loading sync status...</span>
          </div>
        </div>
      </div>
    `;
  }

  async loadData() {
    try {
      this.showLoading();
      
      // Load new wafers data
      const response = await fetch('/api/icarium/check-new-wafers?hours=24');
      const data = await response.json();
      
      if (data.success) {
        this.renderSyncStatus(data);
      } else {
        this.showError('Failed to load sync status');
      }
      
    } catch (error) {
      console.error('❌ Error loading Icarium sync data:', error);
      this.showError('Network error loading sync status');
    }
  }

  renderSyncStatus(data) {
    const { summary, unsynced_wafers } = data;
    const content = this.container.querySelector('.widget-content');

    // Determine status color and icon
    let statusClass = 'success';
    let statusIcon = 'check-circle';
    let statusText = 'All synced';

    if (summary.total_unsynced_wafers > 20) {
      statusClass = 'error';
      statusIcon = 'exclamation-triangle';
      statusText = 'High priority';
    } else if (summary.total_unsynced_wafers > 5) {
      statusClass = 'warning';
      statusIcon = 'exclamation-circle';
      statusText = 'Needs attention';
    } else if (summary.total_unsynced_wafers > 0) {
      statusClass = 'info';
      statusIcon = 'info-circle';
      statusText = 'Minor sync needed';
    }

    // Add attention-grabbing animation for unsynced wafers
    if (summary.total_unsynced_wafers > 0) {
      this.container.classList.add('needs-attention');
    } else {
      this.container.classList.remove('needs-attention');
    }

    content.innerHTML = `
      <div class="sync-status ${statusClass}">
        <div class="status-header">
          <i class="fas fa-${statusIcon} status-icon"></i>
          <span class="status-text">${statusText}</span>
        </div>
        
        <div class="sync-metrics">
          <div class="metric">
            <div class="metric-value">${summary.total_new_wafers}</div>
            <div class="metric-label">New wafers (24h)</div>
          </div>
          <div class="metric">
            <div class="metric-value">${summary.total_unsynced_wafers}</div>
            <div class="metric-label">Need sync</div>
          </div>
          <div class="metric">
            <div class="metric-value">${summary.sync_percentage}%</div>
            <div class="metric-label">Sync rate</div>
          </div>
        </div>
        
        ${this.renderLotsSummary(summary.lots_summary)}
        ${this.renderActionButtons(summary)}
      </div>
      
      <div class="widget-footer">
        <small class="last-updated">
          Last updated: ${new Date().toLocaleTimeString()}
        </small>
      </div>
    `;
  }

  renderLotsSummary(lotsSummary) {
    if (!lotsSummary || Object.keys(lotsSummary).length === 0) {
      return '';
    }

    const lotsWithUnsynced = Object.entries(lotsSummary)
      .filter(([_, counts]) => counts.unsynced > 0)
      .slice(0, 3); // Show top 3 lots

    if (lotsWithUnsynced.length === 0) {
      return '';
    }

    const lotsHtml = lotsWithUnsynced.map(([lotId, counts]) => `
      <div class="lot-item">
        <span class="lot-id">${lotId}</span>
        <span class="lot-count">${counts.unsynced}/${counts.total}</span>
      </div>
    `).join('');

    return `
      <div class="lots-summary">
        <div class="lots-header">
          <i class="fas fa-boxes"></i>
          <span>Lots needing sync:</span>
        </div>
        <div class="lots-list">
          ${lotsHtml}
        </div>
      </div>
    `;
  }

  renderActionButtons(summary) {
    if (summary.total_unsynced_wafers === 0) {
      return `
        <div class="action-buttons">
          <button class="btn-action btn-success" disabled>
            <i class="fas fa-check"></i>
            All synced
          </button>
        </div>
      `;
    }

    return `
      <div class="action-buttons">
        <button class="btn-action btn-primary" onclick="icariumSyncWidget.quickSync()">
          <i class="fas fa-sync"></i>
          Quick Sync (${summary.total_unsynced_wafers})
        </button>
        <button class="btn-action btn-secondary" onclick="icariumSyncWidget.viewDetails()">
          <i class="fas fa-list"></i>
          View Details
        </button>
      </div>
    `;
  }

  showLoading() {
    const content = this.container.querySelector('.widget-content');
    content.innerHTML = `
      <div class="loading-state">
        <i class="fas fa-spinner fa-spin"></i>
        <span>Loading sync status...</span>
      </div>
    `;
  }

  showError(message) {
    const content = this.container.querySelector('.widget-content');
    content.innerHTML = `
      <div class="error-state">
        <i class="fas fa-exclamation-triangle"></i>
        <span>${message}</span>
        <button class="btn-retry" onclick="icariumSyncWidget.loadData()">
          <i class="fas fa-redo"></i>
          Retry
        </button>
      </div>
    `;
  }

  async quickSync() {
    try {
      // Show loading state
      const button = event.target.closest('.btn-action');
      const originalHtml = button.innerHTML;
      button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Syncing...';
      button.disabled = true;

      // Get recommendations first
      const recResponse = await fetch('/api/icarium/sync-recommendations');
      const recData = await recResponse.json();

      if (!recData.success || !recData.recommendations.length) {
        throw new Error('No sync recommendations available');
      }

      // Find batch sync recommendations
      const batchRecs = recData.recommendations.filter(r => r.type === 'batch_sync');
      
      if (batchRecs.length > 0) {
        // Show batch sync dialog
        this.showBatchSyncDialog(batchRecs);
      } else {
        // Show individual sync options
        this.showIndividualSyncDialog(recData.recommendations);
      }

      // Restore button
      button.innerHTML = originalHtml;
      button.disabled = false;

    } catch (error) {
      console.error('❌ Quick sync error:', error);
      this.showNotification('Quick sync failed: ' + error.message, 'error');
      
      // Restore button
      const button = event.target.closest('.btn-action');
      button.innerHTML = originalHtml;
      button.disabled = false;
    }
  }

  showBatchSyncDialog(batchRecommendations) {
    const recommendations = batchRecommendations.slice(0, 5); // Top 5
    const recHtml = recommendations.map(rec => `
      <div class="batch-rec-item">
        <div class="rec-info">
          <strong>${rec.title}</strong>
          <p>${rec.description}</p>
        </div>
        <button class="btn-sync-lot" data-lot-id="${rec.lot_id}" data-wafer-count="${rec.wafer_count}">
          Sync ${rec.wafer_count} wafers
        </button>
      </div>
    `).join('');

    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: '🔄 Quick Sync Options',
        html: `
          <div class="batch-sync-dialog">
            <p>Select lots to sync in batches:</p>
            <div class="batch-recommendations">
              ${recHtml}
            </div>
          </div>
        `,
        width: '600px',
        showConfirmButton: false,
        showCancelButton: true,
        cancelButtonText: 'Close'
      });

      // Add event listeners to sync buttons
      document.querySelectorAll('.btn-sync-lot').forEach(btn => {
        btn.addEventListener('click', (e) => {
          const lotId = e.target.dataset.lotId;
          const waferCount = e.target.dataset.waferCount;
          this.syncLot(lotId, waferCount);
        });
      });
    }
  }

  async syncLot(lotId, waferCount) {
    try {
      // This would need to be implemented based on your batch sync API
      this.showNotification(`Syncing ${waferCount} wafers from lot ${lotId}...`, 'info');
      
      // Refresh data after sync
      setTimeout(() => {
        this.loadData();
      }, 2000);
      
    } catch (error) {
      console.error('❌ Lot sync error:', error);
      this.showNotification('Lot sync failed: ' + error.message, 'error');
    }
  }

  viewDetails() {
    // Navigate to inventory page with Icarium filter
    window.location.href = '/inventory?filter=icarium_sync';
  }

  showSettings() {
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        title: '⚙️ Icarium Sync Settings',
        html: `
          <div class="sync-settings">
            <div class="setting-item">
              <label>
                <input type="checkbox" id="autoRefresh" checked>
                Auto-refresh every 5 minutes
              </label>
            </div>
            <div class="setting-item">
              <label>
                <input type="checkbox" id="showNotifications" checked>
                Show desktop notifications
              </label>
            </div>
            <div class="setting-item">
              <label for="refreshInterval">Refresh interval (minutes):</label>
              <select id="refreshInterval">
                <option value="1">1 minute</option>
                <option value="5" selected>5 minutes</option>
                <option value="10">10 minutes</option>
                <option value="30">30 minutes</option>
              </select>
            </div>
          </div>
        `,
        confirmButtonText: 'Save Settings',
        showCancelButton: true
      }).then((result) => {
        if (result.isConfirmed) {
          this.saveSettings();
        }
      });
    }
  }

  saveSettings() {
    // Save settings to localStorage
    const settings = {
      autoRefresh: document.getElementById('autoRefresh').checked,
      showNotifications: document.getElementById('showNotifications').checked,
      refreshInterval: parseInt(document.getElementById('refreshInterval').value)
    };
    
    localStorage.setItem('icariumSyncSettings', JSON.stringify(settings));
    
    // Apply settings
    this.refreshInterval = settings.refreshInterval * 60 * 1000;
    this.startAutoRefresh();
    
    this.showNotification('Settings saved successfully', 'success');
  }

  startAutoRefresh() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
    
    this.intervalId = setInterval(() => {
      this.loadData();
    }, this.refreshInterval);
  }

  showNotification(message, type = 'info') {
    if (typeof Swal !== 'undefined') {
      Swal.fire({
        text: message,
        icon: type,
        toast: true,
        position: 'top-end',
        showConfirmButton: false,
        timer: 3000
      });
    } else {
      console.log(`${type.toUpperCase()}: ${message}`);
    }
  }

  destroy() {
    if (this.intervalId) {
      clearInterval(this.intervalId);
    }
  }
}

// Initialize widget when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
  // Only initialize if container exists
  if (document.getElementById('icarium-sync-widget')) {
    window.icariumSyncWidget = new IcariumSyncWidget('icarium-sync-widget');
  }
});
